import React from 'react';
import { ServicePageTemplate } from '@/components/templates/ServicePageTemplate';
import { Link } from 'react-router-dom';
import {
  Bot,
  MessageSquare,
  TrendingUp,
  Users,
  Zap,
  Shield,
  Clock,
  Target,
  Calendar,
  BarChart2,
  CheckCircle,
  ArrowRight,
  DollarSign,
  AlertTriangle
} from 'lucide-react';
import { ServiceCTA } from '@/components/services/ServiceCTA';
import {
  ClientTestimonial,
  ClientLogos,
  ROICalculator,
  PricingCard,
  UrgencyBanner,
  TrustSignals
} from '@/components/ui/service-enhancements';

// Extend the problem point type to include icon
type ProblemPoint = {
  icon: React.ReactNode;
  text: string;
};

// Extend the solution feature type to include gradient
type SolutionFeature = {
  title: string;
  description: string;
  icon: React.ReactNode;
  gradient?: string;
};

// Extend the proof item type to include bgColor
type ProofItem = {
  metric: string;
  description: string;
  icon: React.ReactNode;
  bgColor?: string;
};

export default function CustomAISolutions() {
  // Client testimonials data
  const testimonials = [
    {
      name: "<PERSON>",
      role: "Operations Manager",
      company: "TechFlow Solutions",
      image: "/assets/images/testimonials/sarah-chen.jpg",
      testimonial: "Our Smart Business Assistant has been a game-changer. We've recovered $3,200 in monthly revenue from after-hours inquiries alone, and our customer satisfaction scores have increased by 34%. The ROI was evident within the first month.",
      metrics: [
        { label: "Revenue Recovery", value: "$3,200/mo" },
        { label: "Response Time", value: "< 30 sec" },
        { label: "Customer Satisfaction", value: "+34%" }
      ],
      rating: 5
    },
    {
      name: "Marcus Rodriguez",
      role: "CEO",
      company: "GrowthLab Marketing",
      image: "/assets/images/testimonials/marcus-rodriguez.jpg",
      testimonial: "The AI assistant handles 78% of our customer inquiries automatically, freeing up our team to focus on high-value activities. We've seen a 290% ROI in just 8 months, and our customers love the instant responses.",
      metrics: [
        { label: "Automation Rate", value: "78%" },
        { label: "ROI", value: "290%" },
        { label: "Team Efficiency", value: "+45%" }
      ],
      rating: 5
    }
  ];

  // Client logos data
  const clientLogos = [
    { name: "TechFlow Solutions", logo: "/assets/images/clients/techflow.png", industry: "Technology" },
    { name: "GrowthLab Marketing", logo: "/assets/images/clients/growthlab.png", industry: "Marketing" },
    { name: "MedCare Plus", logo: "/assets/images/clients/medcare.png", industry: "Healthcare" },
    { name: "RetailMax", logo: "/assets/images/clients/retailmax.png", industry: "Retail" },
    { name: "FinanceFirst", logo: "/assets/images/clients/financefirst.png", industry: "Finance" },
    { name: "EduTech Pro", logo: "/assets/images/clients/edutech.png", industry: "Education" }
  ];

  // ROI Calculator configuration
  const roiCalculatorInputs = [
    {
      label: "Monthly Customer Inquiries",
      key: "inquiries",
      type: "number" as const,
      defaultValue: 500,
      suffix: "inquiries"
    },
    {
      label: "Average Order Value",
      key: "orderValue",
      type: "number" as const,
      defaultValue: 150,
      prefix: "$"
    },
    {
      label: "Current After-Hours Conversion Rate",
      key: "currentConversion",
      type: "number" as const,
      defaultValue: 5,
      suffix: "%"
    },
    {
      label: "Business Type",
      key: "businessType",
      type: "select" as const,
      options: ["E-commerce", "Professional Services", "SaaS", "Healthcare", "Other"],
      defaultValue: "E-commerce"
    }
  ];

  const calculateROI = (inputs: Record<string, any>) => {
    const monthlyInquiries = inputs.inquiries || 0;
    const orderValue = inputs.orderValue || 0;
    const currentConversion = (inputs.currentConversion || 0) / 100;

    // AI typically improves after-hours conversion by 3-5x
    const aiConversionRate = Math.min(currentConversion * 4, 0.25); // Cap at 25%
    const additionalConversions = monthlyInquiries * 0.3 * (aiConversionRate - currentConversion); // 30% of inquiries are after-hours
    const monthlySavings = additionalConversions * orderValue;
    const annualSavings = monthlySavings * 12;

    // Assume $297/month service cost
    const monthlyCost = 297;
    const roi = ((monthlySavings - monthlyCost) / monthlyCost) * 100;
    const paybackPeriod = monthlyCost / Math.max(monthlySavings - monthlyCost, 1);

    return {
      monthlySavings: Math.round(monthlySavings),
      annualSavings: Math.round(annualSavings),
      roi: Math.round(roi),
      paybackPeriod: Math.round(paybackPeriod)
    };
  };

  // Pricing data
  const pricingPlans = [
    {
      title: "Starter",
      description: "Perfect for small businesses getting started with AI support",
      startingPrice: "$297/mo",
      features: [
        "Up to 1,000 conversations/month",
        "Basic AI training and setup",
        "Email and chat integration",
        "Standard business hours support",
        "Basic analytics dashboard"
      ]
    },
    {
      title: "Professional",
      description: "Ideal for growing businesses with higher volume needs",
      startingPrice: "$597/mo",
      features: [
        "Up to 5,000 conversations/month",
        "Advanced AI training and customization",
        "Multi-channel integration (email, chat, SMS)",
        "24/7 priority support",
        "Advanced analytics and reporting",
        "Custom integrations (CRM, booking systems)"
      ],
      popular: true
    },
    {
      title: "Enterprise",
      description: "Custom solutions for large organizations",
      startingPrice: "Custom",
      features: [
        "Unlimited conversations",
        "Fully customized AI training",
        "White-label solution available",
        "Dedicated account manager",
        "Custom integrations and workflows",
        "SLA guarantees and premium support"
      ]
    }
  ];
  // Problem points with icons and specific cost data
  const problemPoints: ProblemPoint[] = [
    {
      icon: <DollarSign className="w-6 h-6 text-red-500 mt-0.5 flex-shrink-0" />,
      text: "Average SMB loses $2,400 monthly from missed after-hours inquiries (Salesforce 2024)"
    },
    {
      icon: <Clock className="w-6 h-6 text-red-500 mt-0.5 flex-shrink-0" />,
      text: "67% of customers expect 24/7 support availability, yet only 23% of businesses provide it (Microsoft)"
    },
    {
      icon: <Users className="w-6 h-6 text-red-500 mt-0.5 flex-shrink-0" />,
      text: "Round-the-clock staffing costs $156,000+ annually per support agent (Bureau of Labor Statistics)"
    },
    {
      icon: <TrendingUp className="w-6 h-6 text-red-500 mt-0.5 flex-shrink-0" />,
      text: "Businesses lose 27% of potential sales from unanswered inquiries within first hour (Harvard Business Review)"
    },
    {
      icon: <MessageSquare className="w-6 h-6 text-red-500 mt-0.5 flex-shrink-0" />,
      text: "Inconsistent service quality leads to 32% customer churn rate (Zendesk Customer Experience Trends)"
    },
    {
      icon: <AlertTriangle className="w-6 h-6 text-red-500 mt-0.5 flex-shrink-0" />,
      text: "Manual customer service processes are 5x more prone to errors than automated systems (McKinsey)"
    }
  ];

  // Solution features with icons and gradients
  const solutionFeatures: SolutionFeature[] = [
    {
      title: "24/7 Availability",
      description: "Your business never sleeps with our AI assistant handling customer inquiries around the clock.",
      icon: <Clock className="w-6 h-6 text-ethos-purple" />,
      gradient: 'from-blue-50 to-purple-50'
    },
    {
      title: "Sales & Support",
      description: "Convert more leads and provide instant support to boost customer satisfaction and sales.",
      icon: <TrendingUp className="w-6 h-6 text-ethos-purple" />,
      gradient: 'from-purple-50 to-pink-50'
    },
    {
      title: "Booking Management",
      description: "Seamlessly manage appointments and reservations with our integrated booking system.",
      icon: <Calendar className="w-6 h-6 text-ethos-purple" />,
      gradient: 'from-pink-50 to-blue-50'
    },
    {
      title: "Natural Conversations",
      description: "AI that understands and responds naturally, just like a human team member would.",
      icon: <MessageSquare className="w-6 h-6 text-ethos-purple" />,
      gradient: 'from-blue-50 to-indigo-50'
    },
    {
      title: "Process Automation",
      description: "Automate repetitive tasks and focus on growing your business.",
      icon: <Zap className="w-6 h-6 text-ethos-purple" />,
      gradient: 'from-indigo-50 to-purple-50'
    },
    {
      title: "Continuous Learning",
      description: "The more it interacts, the better it gets at serving your business needs.",
      icon: <BarChart2 className="w-6 h-6 text-ethos-purple" />,
      gradient: 'from-purple-50 to-pink-50'
    }
  ];

  // Proof points with credible ROI metrics
  const proofItems: ProofItem[] = [
    {
      metric: "$2,400",
      description: "Average monthly revenue recovery for SMBs using 24/7 AI support (Salesforce)",
      icon: <DollarSign className="w-8 h-8 text-white" />,
      bgColor: 'bg-green-500/90'
    },
    {
      metric: "320%",
      description: "Average ROI achieved by businesses using AI customer support within 12 months (IBM)",
      icon: <TrendingUp className="w-8 h-8 text-white" />,
      bgColor: 'bg-ethos-purple/90'
    },
    {
      metric: "89%",
      description: "Reduction in customer service costs with AI automation (Juniper Research)",
      icon: <BarChart2 className="w-8 h-8 text-white" />,
      bgColor: 'bg-ethos-navy/90'
    }
  ];

  return (
    <ServicePageTemplate
      // Hero Section
      heroTitle={
        <>
          <div className="space-y-3 sm:space-y-4">
            <h2 className="text-4xl md:text-5xl lg:text-6xl font-bold text-ethos-navy leading-tight">
              Smart Business Assistant
            </h2>
            <div className="space-y-3 sm:space-y-4">
              <p className="text-xl md:text-2xl lg:text-3xl font-normal text-ethos-purple/90 leading-tight">
                Your 24/7 AI-Powered Team Member
              </p>
              <p className="text-xl md:text-2xl lg:text-3xl font-normal text-ethos-navy/80 leading-tight">
                Never Miss an Opportunity Again
              </p>
            </div>
          </div>
        </>
      }
      heroSubtitle="Transform your business with AI"
      heroDescription={
        <div className="space-y-4">
          <p className="text-lg text-gray-700">
            Our Smart Business Assistant handles daily operations, answers customer queries, and drives sales 
            around the clock, just like a skilled team member who never sleeps.
          </p>
          <ul className="space-y-2 text-left text-gray-700">
            <li className="flex items-start">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span>24/7 customer support with instant responses</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span>Seamless booking and reservation management</span>
            </li>
            <li className="flex items-start">
              <CheckCircle className="w-5 h-5 text-green-500 mr-2 mt-0.5 flex-shrink-0" />
              <span>Natural, human-like conversations</span>
            </li>
          </ul>
        </div>
      }
      heroImage="/assets/images/CAS.png"
      heroImageAlt="Smart Business Assistant in action"

      // Problem Statement Section
      problemTitle={
        <>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
            <span className="text-ethos-navy">The High Cost of Not Having a </span>
            <span className="text-ethos-purple">Smart Business Assistant</span>
          </h2>
        </>
      }
      problemDescription="Every day without 24/7 AI support costs your business money. The average SMB loses $2,400 monthly from missed after-hours inquiries, while enterprises can lose $50,000+ from abandoned carts and delayed responses. With 67% of customers expecting round-the-clock support, can you afford to keep losing revenue while you sleep?"
      problemPoints={problemPoints}

      // Solution Section
      solutionTitle={
        <>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
            <span className="text-ethos-navy">Smart Business Assistant </span>
            <span className="text-ethos-purple">Features</span>
          </h2>
        </>
      }
      solutionDescription="Our AI-powered assistant is designed to handle all aspects of customer interaction, providing seamless support and driving business growth while you focus on what matters most."
      solutionFeatures={solutionFeatures}

      // Proof/Evidence Section
      proofTitle={
        <>
          <h2 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-medium leading-tight tracking-[-0.02em] mb-6">
            <span className="text-ethos-navy">Real Business </span>
            <span className="text-ethos-purple">Impact</span>
          </h2>
        </>
      }
      proofDescription="Businesses using our Smart Business Assistant have seen remarkable improvements in their operations and customer satisfaction."
      proofItems={proofItems}

      // CTA Section
      ctaSection={
        <ServiceCTA
          titlePart1="Ready to Transform Your Business"
          titlePart2="with AI?"
          description="Get started with your Smart Business Assistant today and experience the future of customer engagement. No credit card required, get started in minutes with our 14-day free trial."
          buttonText="Talk to an AI Specialist"
          buttonLink="/contact?source=ai-solutions"
        />
      }
    />

    {/* Enhanced Sections */}

    {/* Urgency Banner */}
    <UrgencyBanner
      message="🔥 Limited Time: Get your Smart Business Assistant set up in 48 hours with our Fast-Track Implementation"
      ctaText="Claim Fast-Track Setup"
      ctaLink="/contact?source=ai-solutions-urgent"
      expiryDate="December 31, 2024"
      limitedSlots={5}
    />

    {/* Client Testimonials */}
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-ethos-navy mb-4">
            Real Results from Real Businesses
          </h2>
          <p className="text-xl text-ethos-gray max-w-3xl mx-auto">
            See how businesses like yours are recovering lost revenue and improving customer satisfaction with our Smart Business Assistant.
          </p>
        </div>
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {testimonials.map((testimonial, index) => (
            <ClientTestimonial key={index} {...testimonial} />
          ))}
        </div>
      </div>
    </section>

    {/* Client Logos */}
    <ClientLogos
      title="Trusted by 200+ Growing Businesses"
      logos={clientLogos}
    />

    {/* ROI Calculator */}
    <section className="py-16 bg-gray-50">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-ethos-navy mb-4">
            Calculate Your Potential ROI
          </h2>
          <p className="text-xl text-ethos-gray">
            See how much revenue you could recover with 24/7 AI support
          </p>
        </div>
        <ROICalculator
          title="Smart Business Assistant ROI Calculator"
          description="Enter your business details below to see your potential return on investment with our AI support solution."
          inputs={roiCalculatorInputs}
          calculation={calculateROI}
        />
      </div>
    </section>

    {/* Pricing Section */}
    <section className="py-16 bg-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-ethos-navy mb-4">
            Simple, Transparent Pricing
          </h2>
          <p className="text-xl text-ethos-gray mb-8">
            Choose the plan that fits your business needs. All plans include setup, training, and ongoing support.
          </p>
          <div className="inline-flex items-center space-x-2 bg-green-100 text-green-800 px-4 py-2 rounded-full">
            <CheckCircle className="w-5 h-5" />
            <span className="font-medium">30-day money-back guarantee</span>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          {pricingPlans.map((plan, index) => (
            <PricingCard key={index} {...plan} />
          ))}
        </div>
      </div>
    </section>

    {/* Trust Signals */}
    <TrustSignals
      certifications={["SOC 2 Type II", "GDPR Compliant", "ISO 27001"]}
      awards={["Best AI Customer Service 2024", "Innovation Award 2024"]}
      securityBadges={["256-bit SSL Encryption", "PCI DSS Compliant", "Regular Security Audits"]}
    />

    {/* Industry-Specific Examples */}
    <section className="py-16 bg-gray-50">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-ethos-navy mb-4">
            Tailored Solutions for Your Industry
          </h2>
          <p className="text-xl text-ethos-gray">
            Our Smart Business Assistant adapts to your specific industry needs
          </p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold text-ethos-navy mb-3">E-commerce</h3>
            <ul className="space-y-2 text-ethos-gray">
              <li>• Order tracking and status updates</li>
              <li>• Product recommendations</li>
              <li>• Cart abandonment recovery</li>
              <li>• Return and refund processing</li>
            </ul>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold text-ethos-navy mb-3">Professional Services</h3>
            <ul className="space-y-2 text-ethos-gray">
              <li>• Appointment scheduling</li>
              <li>• Service inquiries and quotes</li>
              <li>• Client onboarding</li>
              <li>• Follow-up and feedback collection</li>
            </ul>
          </div>
          <div className="bg-white p-6 rounded-lg shadow-lg">
            <h3 className="text-xl font-semibold text-ethos-navy mb-3">Healthcare</h3>
            <ul className="space-y-2 text-ethos-gray">
              <li>• Appointment booking and reminders</li>
              <li>• Insurance verification</li>
              <li>• Prescription refill requests</li>
              <li>• General health inquiries</li>
            </ul>
          </div>
        </div>
      </div>
    </section>

    {/* FAQ Section */}
    <section className="py-16 bg-white">
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-12">
          <h2 className="text-3xl sm:text-4xl font-bold text-ethos-navy mb-4">
            Frequently Asked Questions
          </h2>
        </div>
        <div className="space-y-6">
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-ethos-navy mb-2">How quickly can we get started?</h3>
            <p className="text-ethos-gray">With our Fast-Track Implementation, your Smart Business Assistant can be live and handling customer inquiries within 48 hours. Full customization and training typically takes 1-2 weeks.</p>
          </div>
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-ethos-navy mb-2">What if our team isn't technical?</h3>
            <p className="text-ethos-gray">No technical expertise required! We handle all the setup, training, and integration. Your team just needs to review and approve the AI responses during the training phase.</p>
          </div>
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-ethos-navy mb-2">How do you ensure data security?</h3>
            <p className="text-ethos-gray">We use bank-level 256-bit SSL encryption, are SOC 2 Type II certified, and undergo regular security audits. Your customer data is never shared or used for training other AI models.</p>
          </div>
          <div className="bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-semibold text-ethos-navy mb-2">What's the real ROI timeline?</h3>
            <p className="text-ethos-gray">Most businesses see positive ROI within 30-60 days. Our clients typically recover their investment through increased after-hours conversions and reduced staffing costs within the first quarter.</p>
          </div>
        </div>
      </div>
    </section>
  );
}